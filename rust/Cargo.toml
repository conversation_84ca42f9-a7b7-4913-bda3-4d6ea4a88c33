[package]
name = "shredstream-decoder"
version = "0.1.0"
edition = "2024"

[lib]
crate-type = ["cdylib"]

[dependencies]
bincode = "1.3.3"
js-sys = "0.3"
serde-wasm-bindgen = "0.6.5"
solana-entry = { version = "2.2.7", default-features = false }
wasm-bindgen = "0.2"

[target.'cfg(target_arch = "wasm32")'.dependencies]
getrandom = { version = "0.3.3", features = ["wasm_js"] }
agave-precompiles = { version = "2.2.7", default-features = false }
solana-secp256r1-program = { version = "2.2.2", default-features = false }

[patch.crates-io]
openssl = { path = "patches/dummy-openssl" }
openssl-sys = { path = "patches/dummy-openssl-sys" }
