use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct Hash([u8; 32]);

#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Transaction {
    pub signatures: Vec<[u8; 64]>,
    pub message: Vec<u8>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Entry {
    pub num_hashes: u64,
    pub hash: Hash,
    pub transactions: Vec<Transaction>,
}

#[wasm_bindgen]
pub fn decode_entries(bytes: &[u8]) -> Result<JsValue, JsValue> {
    let v: Vec<Entry> = bincode::deserialize(bytes)
        .map_err(|e| JsValue::from_str(&format!("Failed to deserialize entries: {}", e)))?;

    serde_wasm_bindgen::to_value(&v)
        .map_err(|e| JsValue::from_str(&format!("Failed to convert to JsValue: {}", e)))
}
