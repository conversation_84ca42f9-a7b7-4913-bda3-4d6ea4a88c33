{"rustc": 15497389221046826682, "features": "[\"alloc\", \"futures-io\", \"io\", \"memchr\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 336243669335521001, "path": 17099334823361099506, "deps": [[5103565458935487, "futures_io", false, 1502803818848344421], [1615478164327904835, "pin_utils", false, 17283838933012124672], [1906322745568073236, "pin_project_lite", false, 4379106028863866381], [3129130049864710036, "memchr", false, 12015221177227729297], [6955678925937229351, "slab", false, 15690900569651618614], [7620660491849607393, "futures_core", false, 13021913760991176250], [16240732885093539806, "futures_task", false, 17781379249871933515]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-a25f8754d6b50155/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}