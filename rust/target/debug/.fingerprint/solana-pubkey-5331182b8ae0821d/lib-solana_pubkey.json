{"rustc": 15497389221046826682, "features": "[\"bytemuck\", \"curve25519\", \"serde\", \"sha2\"]", "declared_features": "[\"borsh\", \"bytemuck\", \"curve25519\", \"default\", \"dev-context-only-utils\", \"frozen-abi\", \"rand\", \"serde\", \"sha2\", \"std\"]", "target": 6003644542170993130, "profile": 13904601386562544002, "path": 7846583149578050980, "deps": [[4145337479814240220, "solana_decode_error", false, 16688978623064089504], [5157631553186200874, "num_traits", false, 15725039735389927164], [5646760554799980987, "five8_const", false, 15917222015871582288], [6616501577376279788, "bs58", false, 4948370421785504352], [9689903380558560274, "serde", false, 16038530611200074664], [11104455582174147483, "solana_sha256_hasher", false, 2564990000635851409], [13595581133353633439, "curve25519_dalek", false, 13944369639789967891], [14074610438553418890, "bytemuck", false, 13539438320978631739], [14254950316256772154, "solana_atomic_u64", false, 13272458061118934806], [15246557919602675095, "bytemuck_derive", false, 1906003913717083562], [15429715045911386410, "solana_sanitize", false, 209894361229393635], [16257276029081467297, "serde_derive", false, 18328629502287840784]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-pubkey-5331182b8ae0821d/dep-lib-solana_pubkey", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}