{"rustc": 15497389221046826682, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 3033921117576893, "path": 5134834939135515267, "deps": [[1988483478007900009, "unicode_ident", false, 11801641621428967004], [3060637413840920116, "proc_macro2", false, 9689789723025982531], [17990358020177143287, "quote", false, 16463695800006207511]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-0cfc047599563e70/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}