{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 6875603382767429092, "profile": 4305681822225477910, "path": 10437665824755753453, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 9717213118447830190], [17990358020177143287, "quote", false, 6975534730328972777]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-36a919eb4bd34eae/dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}