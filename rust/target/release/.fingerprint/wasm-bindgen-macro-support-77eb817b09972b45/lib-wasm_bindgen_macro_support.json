{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 4305681822225477910, "path": 10215168194706528797, "deps": [[3060637413840920116, "proc_macro2", false, 16812528154537222832], [14299170049494554845, "wasm_bindgen_shared", false, 2954769940173674965], [14372503175394433084, "wasm_bindgen_backend", false, 18362391368551235965], [17990358020177143287, "quote", false, 6975534730328972777], [18149961000318489080, "syn", false, 2911894094829615767]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-support-77eb817b09972b45/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}