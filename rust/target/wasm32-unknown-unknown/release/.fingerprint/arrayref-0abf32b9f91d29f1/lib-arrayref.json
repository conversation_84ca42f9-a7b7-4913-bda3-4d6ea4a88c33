{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 14855336370480542997, "profile": 2040997289075261528, "path": 3165773899054839503, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/arrayref-0abf32b9f91d29f1/dep-lib-arrayref", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}