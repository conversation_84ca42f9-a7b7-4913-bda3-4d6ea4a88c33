{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 13060062996227388079, "profile": 2040997289075261528, "path": 8113060715237910958, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/base64-8005e432d049afe5/dep-lib-base64", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}