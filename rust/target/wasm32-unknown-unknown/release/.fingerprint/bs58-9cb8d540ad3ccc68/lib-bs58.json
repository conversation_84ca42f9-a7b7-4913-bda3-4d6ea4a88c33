{"rustc": 15497389221046826682, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"cb58\", \"check\", \"default\", \"sha2\", \"smallvec\", \"std\", \"tinyvec\"]", "target": 2243021261112611720, "profile": 2040997289075261528, "path": 357559576345532964, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/bs58-9cb8d540ad3ccc68/dep-lib-bs58", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}