{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4468123440088164316, "build_script_build", false, 9180123219008215326]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/crossbeam-utils-8586bd15c5575907/output", "paths": ["no_atomic.rs"]}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}