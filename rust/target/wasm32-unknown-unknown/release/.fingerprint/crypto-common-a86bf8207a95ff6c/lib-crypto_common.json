{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"getrandom\", \"rand_core\", \"std\"]", "target": 16242158919585437602, "profile": 2040997289075261528, "path": 11214362463697988850, "deps": [[10520923840501062997, "generic_array", false, 17723833802254774974], [17001665395952474378, "typenum", false, 9054097445005665509]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/crypto-common-a86bf8207a95ff6c/dep-lib-crypto_common", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}