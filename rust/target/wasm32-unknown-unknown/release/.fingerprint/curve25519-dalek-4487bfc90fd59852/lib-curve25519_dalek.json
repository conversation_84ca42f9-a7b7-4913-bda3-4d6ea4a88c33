{"rustc": 15497389221046826682, "features": "[\"alloc\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 4744499769514376500, "profile": 2040997289075261528, "path": 3006486542479493288, "deps": [[1740877332521282793, "rand_core", false, 13936644436752337288], [2932480923465029663, "zeroize", false, 17053814966320016931], [3712811570531045576, "byteorder", false, 6786092253815918080], [6374421995994392543, "digest", false, 11865893975639525246], [17003143334332120809, "subtle", false, 3635282083207970173]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/curve25519-dalek-4487bfc90fd59852/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}