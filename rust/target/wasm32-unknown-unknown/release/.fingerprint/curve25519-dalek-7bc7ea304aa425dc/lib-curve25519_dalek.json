{"rustc": 15497389221046826682, "features": "[\"alloc\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 4744499769514376500, "profile": 2040997289075261528, "path": 3006486542479493288, "deps": [[1740877332521282793, "rand_core", false, 10131511455691256338], [2932480923465029663, "zeroize", false, 16427329829350399969], [3712811570531045576, "byteorder", false, 3667516661788986307], [6374421995994392543, "digest", false, 4949510216817675241], [17003143334332120809, "subtle", false, 12958667999536145557]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/curve25519-dalek-7bc7ea304aa425dc/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}