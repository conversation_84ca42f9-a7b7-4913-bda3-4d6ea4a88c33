{"rustc": 15497389221046826682, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"blobby\", \"dev\", \"std\"]", "target": 7510122432137863311, "profile": 2040997289075261528, "path": 10866092637746334644, "deps": [[10520923840501062997, "generic_array", false, 17723833802254774974]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/digest-5a5f716980108bcc/dep-lib-digest", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}