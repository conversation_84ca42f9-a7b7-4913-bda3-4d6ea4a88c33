{"rustc": 15497389221046826682, "features": "[\"alloc\", \"block-buffer\", \"core-api\", \"default\", \"mac\", \"std\", \"subtle\"]", "declared_features": "[\"alloc\", \"blobby\", \"block-buffer\", \"const-oid\", \"core-api\", \"default\", \"dev\", \"mac\", \"oid\", \"rand_core\", \"std\", \"subtle\"]", "target": 7510122432137863311, "profile": 2040997289075261528, "path": 7285135740383288311, "deps": [[2352660017780662552, "crypto_common", false, 3085362371127971189], [10626340395483396037, "block_buffer", false, 2051700328215657042], [17003143334332120809, "subtle", false, 12958667999536145557]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/digest-77fc1158b1548fe8/dep-lib-digest", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}