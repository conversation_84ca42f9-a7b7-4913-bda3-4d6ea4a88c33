{"rustc": 15497389221046826682, "features": "[\"default\", \"rand\", \"serde_crate\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"batch_deterministic\", \"default\", \"legacy_compatibility\", \"merlin\", \"nightly\", \"rand\", \"rand_core\", \"serde\", \"serde_bytes\", \"serde_crate\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 16409354033026609460, "profile": 2040997289075261528, "path": 17749628708327961150, "deps": [[2932480923465029663, "zeroize", false, 16427329829350399969], [4731167174326621189, "rand", false, 1861440920011457567], [9431183304631869056, "curve25519_dalek", false, 6032034766820388570], [9689903380558560274, "serde_crate", false, 5238837755771194787], [11472355562936271783, "sha2", false, 14850985388288803523], [16629266738323756185, "ed25519", false, 10293478384595438818]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/ed25519-dalek-938a0b4bebacd968/dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}