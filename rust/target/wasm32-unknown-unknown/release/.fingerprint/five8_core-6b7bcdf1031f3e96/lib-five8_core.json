{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"std\"]", "target": 10889037578358917193, "profile": 2040997289075261528, "path": 13721865877032596034, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/five8_core-6b7bcdf1031f3e96/dep-lib-five8_core", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}