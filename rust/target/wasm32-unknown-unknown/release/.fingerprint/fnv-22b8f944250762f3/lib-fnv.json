{"rustc": 15497389221046826682, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"std\"]", "target": 10248144769085601448, "profile": 2040997289075261528, "path": 8108119943243159921, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/fnv-22b8f944250762f3/dep-lib-fnv", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}