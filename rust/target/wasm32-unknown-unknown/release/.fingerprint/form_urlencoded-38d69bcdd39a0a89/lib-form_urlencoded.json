{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 9489767039844467200, "profile": 2040997289075261528, "path": 4101751116963844924, "deps": [[40386456601120721, "percent_encoding", false, 16970004538222351709]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/form_urlencoded-38d69bcdd39a0a89/dep-lib-form_urlencoded", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}