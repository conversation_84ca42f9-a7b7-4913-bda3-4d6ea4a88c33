{"rustc": 15497389221046826682, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"cfg-target-has-atomic\", \"default\", \"std\", \"unstable\"]", "target": 13518091470260541623, "profile": 18348216721672176038, "path": 13482709877904102086, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/futures-task-b88b86869271502f/dep-lib-futures_task", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}