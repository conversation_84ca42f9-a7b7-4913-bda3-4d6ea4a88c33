{"rustc": 15497389221046826682, "features": "[\"alloc\", \"futures-io\", \"io\", \"memchr\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 17099334823361099506, "deps": [[5103565458935487, "futures_io", false, 15132639528711472673], [1615478164327904835, "pin_utils", false, 3320524360348164468], [1906322745568073236, "pin_project_lite", false, 629445213474503000], [3129130049864710036, "memchr", false, 8266192041545884620], [6955678925937229351, "slab", false, 1297337251204019562], [7620660491849607393, "futures_core", false, 14232376524556409749], [16240732885093539806, "futures_task", false, 9389413865772469794]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/futures-util-0632b2811bba17ac/dep-lib-futures_util", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}