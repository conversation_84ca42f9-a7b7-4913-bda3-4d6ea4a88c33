{"rustc": 15497389221046826682, "features": "[\"js\", \"js-sys\", \"std\", \"wasm-bindgen\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 2040997289075261528, "path": 13349453684238134133, "deps": [[6946689283190175495, "wasm_bindgen", false, 7867637955086464929], [9003359908906038687, "js_sys", false, 16353966171397067228], [10411997081178400487, "cfg_if", false, 3969964842437940506]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/getrandom-442d0028fa3b08e2/dep-lib-getrandom", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}