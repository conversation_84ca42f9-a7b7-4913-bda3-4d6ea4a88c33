{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[3331586631144870129, "build_script_build", false, 12375894232245104321]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/getrandom-90e6308b2841ee61/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}