{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 2040997289075261528, "path": 10651355782535398832, "deps": [[5170503507811329045, "build_script_build", false, 14140802156196368409], [10411997081178400487, "cfg_if", false, 12883859398113435013]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/getrandom-e3da9c97b5bc5033/dep-lib-getrandom", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}