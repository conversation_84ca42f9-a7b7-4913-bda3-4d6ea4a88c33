{"rustc": 15497389221046826682, "features": "[\"wasm_js\"]", "declared_features": "[\"rustc-dep-of-std\", \"std\", \"wasm_js\"]", "target": 11669924403970522481, "profile": 2896712256932847751, "path": 15265557455003442284, "deps": [[3331586631144870129, "build_script_build", false, 170809385913773833], [6946689283190175495, "wasm_bindgen", false, 1618803851217664853], [10411997081178400487, "cfg_if", false, 12883859398113435013]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/getrandom-e43c51287f195243/dep-lib-getrandom", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}