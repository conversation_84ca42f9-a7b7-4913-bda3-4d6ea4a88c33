{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"reset\", \"std\"]", "target": 12991177224612424488, "profile": 2040997289075261528, "path": 12071220358208104214, "deps": [[17475753849556516473, "digest", false, 13818757374233631636]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/hmac-77fc70bf2421d594/dep-lib-hmac", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}