{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 15193990051491448752, "profile": 2040997289075261528, "path": 16462238383101810806, "deps": [[5392525048748667223, "hmac", false, 1347428659899480197], [6374421995994392543, "digest", false, 11865893975639525246], [10520923840501062997, "generic_array", false, 13622375785974976720]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/hmac-drbg-011459ba308871da/dep-lib-hmac_drbg", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}