{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 15193990051491448752, "profile": 2040997289075261528, "path": 16462238383101810806, "deps": [[5392525048748667223, "hmac", false, 12615903489182221531], [6374421995994392543, "digest", false, 4949510216817675241], [10520923840501062997, "generic_array", false, 17723833802254774974]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/hmac-drbg-410efae3c535c55b/dep-lib-hmac_drbg", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}