{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"asm\", \"no_unroll\", \"simd\"]", "target": 15797377429185147544, "profile": 2040997289075261528, "path": 12489160817047491016, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/keccak-e35c6ffae6a9d280/dep-lib-keccak", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}