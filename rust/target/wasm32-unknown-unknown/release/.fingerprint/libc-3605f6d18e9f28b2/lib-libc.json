{"rustc": 15497389221046826682, "features": "[\"default\", \"std\"]", "declared_features": "[\"align\", \"const-extern-fn\", \"default\", \"extra_traits\", \"rustc-dep-of-std\", \"rustc-std-workspace-core\", \"std\", \"use_std\"]", "target": 17682796336736096309, "profile": 2040997289075261528, "path": 1003556375223180815, "deps": [[2924422107542798392, "build_script_build", false, 10011370962381636432]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/libc-3605f6d18e9f28b2/dep-lib-libc", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}