{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2924422107542798392, "build_script_build", false, 9056101029747813407]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/libc-75f3082760fb72f4/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_FREEBSD_VERSION", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_LINUX_TIME_BITS64", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_GNU_FILE_OFFSET_BITS", "val": null}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}