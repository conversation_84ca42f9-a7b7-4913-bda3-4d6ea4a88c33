{"rustc": 15497389221046826682, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"databake\", \"default\", \"serde\", \"testing\", \"yoke\"]", "target": 6548088149557820361, "profile": 2040997289075261528, "path": 4476190036167797007, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/litemap-88b32cb0566e160d/dep-lib-litemap", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}