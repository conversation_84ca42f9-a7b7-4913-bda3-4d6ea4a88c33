{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8081351675046095464, "build_script_build", false, 10783753908088352746]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/lock_api-9dd293d1a82fc8a2/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}