{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11343705837059611329, "build_script_build", false, 2096157015870537487]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/num-bigint-4c874b00438ae195/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}