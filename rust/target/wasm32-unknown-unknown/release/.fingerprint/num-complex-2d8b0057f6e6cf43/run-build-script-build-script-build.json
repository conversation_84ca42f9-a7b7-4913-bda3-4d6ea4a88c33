{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16638788029644465450, "build_script_build", false, 17633297839402482287]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/num-complex-2d8b0057f6e6cf43/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}