{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13533721945928300150, "build_script_build", false, 8334514337439639238]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/num-rational-0e95358f111e7d03/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}