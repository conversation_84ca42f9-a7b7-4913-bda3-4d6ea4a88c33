{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5157631553186200874, "build_script_build", false, 10913102303641889922]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/num-traits-758a8ada4a53e244/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}