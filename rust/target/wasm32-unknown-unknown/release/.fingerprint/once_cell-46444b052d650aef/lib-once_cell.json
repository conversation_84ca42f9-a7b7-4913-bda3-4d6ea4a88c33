{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"alloc\", \"atomic-polyfill\", \"critical-section\", \"default\", \"parking_lot\", \"portable-atomic\", \"race\", \"std\", \"unstable\"]", "target": 17524666916136250164, "profile": 2040997289075261528, "path": 4976748899586959082, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/once_cell-46444b052d650aef/dep-lib-once_cell", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}