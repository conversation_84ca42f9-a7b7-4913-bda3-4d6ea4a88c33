{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 16026105071940383217, "profile": 2040997289075261528, "path": 6676278704992445108, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/opaque-debug-b89091508e2701f0/dep-lib-opaque_debug", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}