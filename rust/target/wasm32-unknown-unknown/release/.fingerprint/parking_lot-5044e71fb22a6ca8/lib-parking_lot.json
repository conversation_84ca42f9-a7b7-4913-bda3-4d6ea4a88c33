{"rustc": 15497389221046826682, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"hardware-lock-elision\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\"]", "target": 9887373948397848517, "profile": 2040997289075261528, "path": 8373070253151236019, "deps": [[4269498962362888130, "parking_lot_core", false, 14326018858292848377], [8081351675046095464, "lock_api", false, 14381395835098017487]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/parking_lot-5044e71fb22a6ca8/dep-lib-parking_lot", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}