{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4269498962362888130, "build_script_build", false, 879960490626422739]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/parking_lot_core-4d2d731a747b9e9c/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}