{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"default\", \"hmac\", \"parallel\", \"password-hash\", \"rayon\", \"sha1\", \"sha2\", \"simple\", \"std\"]", "target": 9229284490985355380, "profile": 2040997289075261528, "path": 14282267169561111624, "deps": [[17475753849556516473, "digest", false, 13818757374233631636]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/pbkdf2-21be62903ea3fa80/dep-lib-pbkdf2", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}