{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 6142422912982997569, "profile": 2040997289075261528, "path": 11965400397079276224, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/pin-utils-3241b37eb4fdd53c/dep-lib-pin_utils", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}