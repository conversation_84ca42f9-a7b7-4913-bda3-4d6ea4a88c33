{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 18226510971457336415, "deps": [[1333041802001714747, "rand_chacha", false, 10987649364618569185], [1740877332521282793, "rand_core", false, 13936644436752337288], [5170503507811329045, "getrandom_package", false, 2902273775203562914]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/rand-9bd2672950ca743a/dep-lib-rand", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}