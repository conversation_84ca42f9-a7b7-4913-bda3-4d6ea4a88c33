{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"default\", \"use_std\"]", "target": 3556356971060988614, "profile": 2040997289075261528, "path": 6117659648904593384, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/scopeguard-cf6ea964f68e4c86/dep-lib-scopeguard", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}