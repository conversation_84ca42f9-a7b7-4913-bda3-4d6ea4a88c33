{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9689903380558560274, "build_script_build", false, 6073369737593393347]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/serde-5900354c9c915ad2/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}