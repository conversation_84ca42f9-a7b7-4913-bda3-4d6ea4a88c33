{"rustc": 15497389221046826682, "features": "[\"default\", \"derive\", \"serde_derive\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"derive\", \"rc\", \"serde_derive\", \"std\", \"unstable\"]", "target": 16256121404318112599, "profile": 2040997289075261528, "path": 17282395372558467627, "deps": [[9689903380558560274, "build_script_build", false, 8731279273390312299], [16257276029081467297, "serde_derive", false, 5131836204827326872]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/serde-f842f6bcb377967d/dep-lib-serde", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}