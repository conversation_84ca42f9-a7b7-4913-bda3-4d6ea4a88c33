{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[15367738274754116744, "build_script_build", false, 10098665219105654034]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/serde_json-8ab9d1db6d667bcb/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}