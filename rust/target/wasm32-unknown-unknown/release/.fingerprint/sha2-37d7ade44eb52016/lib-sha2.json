{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"asm\", \"asm-aarch64\", \"compress\", \"default\", \"force-soft\", \"sha2-asm\", \"std\"]", "target": 320901375807887087, "profile": 2040997289075261528, "path": 1004688355739847557, "deps": [[6374421995994392543, "digest", false, 4949510216817675241], [10411997081178400487, "cfg_if", false, 12883859398113435013], [12023997170508617523, "block_buffer", false, 1852888627922947153], [13927846409374511869, "opaque_debug", false, 2997776842200896033]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/sha2-37d7ade44eb52016/dep-lib-sha2", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}