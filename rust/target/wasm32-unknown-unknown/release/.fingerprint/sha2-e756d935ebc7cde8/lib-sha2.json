{"rustc": 15497389221046826682, "features": "[\"default\", \"std\"]", "declared_features": "[\"asm\", \"asm-aarch64\", \"compress\", \"default\", \"force-soft\", \"force-soft-compact\", \"loongarch64_asm\", \"oid\", \"sha2-asm\", \"std\"]", "target": 9593554856174113207, "profile": 2040997289075261528, "path": 12263995758996184405, "deps": [[10411997081178400487, "cfg_if", false, 12883859398113435013], [17475753849556516473, "digest", false, 13818757374233631636]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/sha2-e756d935ebc7cde8/dep-lib-sha2", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}