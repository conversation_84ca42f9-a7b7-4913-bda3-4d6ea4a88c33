{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"default\", \"derive-preview\", \"digest\", \"digest-preview\", \"hazmat-preview\", \"rand-preview\", \"rand_core\", \"signature_derive\", \"std\"]", "target": 14677263450862682510, "profile": 2040997289075261528, "path": 12971614864629602869, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/signature-8595dcc44d208d85/dep-lib-signature", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}