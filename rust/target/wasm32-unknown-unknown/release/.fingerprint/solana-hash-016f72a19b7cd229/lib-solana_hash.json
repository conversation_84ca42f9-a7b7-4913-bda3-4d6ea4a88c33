{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"borsh\", \"bytemuck\", \"default\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\", \"std\"]", "target": 14600528003313624360, "profile": 12231795934687494313, "path": 3318170796935383371, "deps": [[6616501577376279788, "bs58", false, 2158659727543328521], [6946689283190175495, "wasm_bindgen", false, 1618803851217664853], [9003359908906038687, "js_sys", false, 11468784709613166181], [14254950316256772154, "solana_atomic_u64", false, 10308383029206511062], [15429715045911386410, "solana_sanitize", false, 7426719212012838396]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/solana-hash-016f72a19b7cd229/dep-lib-solana_hash", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}