{"rustc": 15497389221046826682, "features": "[\"bytemuck\", \"default\", \"serde\", \"std\"]", "declared_features": "[\"borsh\", \"bytemuck\", \"default\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\", \"std\"]", "target": 14600528003313624360, "profile": 12231795934687494313, "path": 3318170796935383371, "deps": [[6616501577376279788, "bs58", false, 586228657894752129], [6946689283190175495, "wasm_bindgen", false, 7867637955086464929], [9003359908906038687, "js_sys", false, 16353966171397067228], [9689903380558560274, "serde", false, 11114023065540642446], [14074610438553418890, "bytemuck", false, 1070605173783209615], [14254950316256772154, "solana_atomic_u64", false, 3734154841998121749], [15246557919602675095, "bytemuck_derive", false, 16148780030231779476], [15429715045911386410, "solana_sanitize", false, 8438729217903120024], [16257276029081467297, "serde_derive", false, 11108096326566441988]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/solana-hash-b26daf4f96c5a408/dep-lib-solana_hash", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}