{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"bincode\", \"borsh\", \"default\", \"frozen-abi\", \"serde\", \"std\"]", "target": 11200884870583074897, "profile": 12231795934687494313, "path": 15770054626379900844, "deps": [[5157631553186200874, "num_traits", false, 5110675131794983689], [6946689283190175495, "wasm_bindgen", false, 1618803851217664853], [9003359908906038687, "js_sys", false, 11468784709613166181], [9475981483390999469, "solana_pubkey", false, 2847527838100901175], [9920160576179037441, "getrandom", false, 9691959118646704042]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/solana-instruction-dc03f7ae3ca5c4a5/dep-lib-solana_instruction", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}