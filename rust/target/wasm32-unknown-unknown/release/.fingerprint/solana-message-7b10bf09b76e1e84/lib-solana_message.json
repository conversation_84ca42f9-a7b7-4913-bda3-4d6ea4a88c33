{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"bincode\", \"blake3\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 7288017095310012413, "profile": 12231795934687494313, "path": 5857062759363820540, "deps": [[1461800729938538396, "solana_instruction", false, 16347219171079655854], [6946689283190175495, "wasm_bindgen", false, 1618803851217664853], [7355047358885037824, "solana_hash", false, 4675245116761986050], [9475981483390999469, "solana_pubkey", false, 2847527838100901175], [9556858120010252096, "solana_transaction_error", false, 17562889544850173154], [14591356476411885690, "solana_sdk_ids", false, 9014208963768544857], [15429715045911386410, "solana_sanitize", false, 7426719212012838396], [17917672826516349275, "lazy_static", false, 2984762410084858386]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/solana-message-7b10bf09b76e1e84/dep-lib-solana_message", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}