{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"borsh\", \"bytemuck\", \"curve25519\", \"default\", \"dev-context-only-utils\", \"frozen-abi\", \"rand\", \"serde\", \"sha2\", \"std\"]", "target": 6003644542170993130, "profile": 12231795934687494313, "path": 7846583149578050980, "deps": [[4145337479814240220, "solana_decode_error", false, 4011180964505208841], [5157631553186200874, "num_traits", false, 5110675131794983689], [5646760554799980987, "five8_const", false, 10146168428743295760], [6616501577376279788, "bs58", false, 2158659727543328521], [6946689283190175495, "wasm_bindgen", false, 1618803851217664853], [9003359908906038687, "js_sys", false, 11468784709613166181], [9920160576179037441, "getrandom", false, 9691959118646704042], [14254950316256772154, "solana_atomic_u64", false, 10308383029206511062], [15429715045911386410, "solana_sanitize", false, 7426719212012838396]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/solana-pubkey-8a09e875046cfe6e/dep-lib-solana_pubkey", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}