{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 14222679030826188001, "profile": 2040997289075261528, "path": 1324226224447315859, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/solana-sanitize-1387890e8fa45996/dep-lib-solana_sanitize", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}