{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 1643282199907566603, "profile": 2040997289075261528, "path": 7307423219923688762, "deps": [[9475981483390999469, "solana_pubkey", false, 2847527838100901175], [9556858120010252096, "solana_transaction_error", false, 17562889544850173154], [15632274837287148962, "solana_signature", false, 11081508745849192656]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/solana-signer-72f43e5fa67b7b62/dep-lib-solana_signer", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}