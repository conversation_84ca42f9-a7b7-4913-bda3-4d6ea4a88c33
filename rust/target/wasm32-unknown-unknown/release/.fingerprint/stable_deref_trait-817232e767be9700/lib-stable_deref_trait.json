{"rustc": 15497389221046826682, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 5317002649412810881, "profile": 2040997289075261528, "path": 9876489540678667685, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/stable_deref_trait-817232e767be9700/dep-lib-stable_deref_trait", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}