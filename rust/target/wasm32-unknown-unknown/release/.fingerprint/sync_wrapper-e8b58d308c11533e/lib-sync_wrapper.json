{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"futures\", \"futures-core\"]", "target": 1703982665153516621, "profile": 2040997289075261528, "path": 17990823930421327078, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/sync_wrapper-e8b58d308c11533e/dep-lib-sync_wrapper", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}