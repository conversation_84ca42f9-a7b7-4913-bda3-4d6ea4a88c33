{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8008191657135824715, "build_script_build", false, 12670889856782184621]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/thiserror-fc8184fb6c18bbfa/output", "paths": ["build/probe.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}