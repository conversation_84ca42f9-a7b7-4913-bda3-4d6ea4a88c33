{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17001665395952474378, "build_script_build", false, 18186062778250924833]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/typenum-bcf1b6e3a6d20813/output", "paths": ["tests"]}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}