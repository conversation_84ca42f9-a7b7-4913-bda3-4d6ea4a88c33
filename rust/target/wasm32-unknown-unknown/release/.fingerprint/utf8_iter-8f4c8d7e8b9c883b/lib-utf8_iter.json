{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 6216520282702351879, "profile": 2040997289075261528, "path": 14151873201621281810, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/utf8_iter-8f4c8d7e8b9c883b/dep-lib-utf8_iter", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}