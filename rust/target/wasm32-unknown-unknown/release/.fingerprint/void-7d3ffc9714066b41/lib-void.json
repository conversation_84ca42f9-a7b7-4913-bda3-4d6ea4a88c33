{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"default\", \"std\"]", "target": 6236763584596485024, "profile": 2040997289075261528, "path": 11733819391981883028, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/void-7d3ffc9714066b41/dep-lib-void", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}