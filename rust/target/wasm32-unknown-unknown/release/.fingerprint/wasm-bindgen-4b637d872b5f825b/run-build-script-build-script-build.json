{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6946689283190175495, "build_script_build", false, 17709837472190120753]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/wasm-bindgen-4b637d872b5f825b/output", "paths": ["build.rs"]}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}