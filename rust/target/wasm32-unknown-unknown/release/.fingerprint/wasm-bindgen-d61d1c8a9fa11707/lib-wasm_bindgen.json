{"rustc": 15497389221046826682, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 8678796594883496969, "path": 1545970865074875010, "deps": [[3722963349756955755, "once_cell", false, 4862141346527847023], [6946689283190175495, "build_script_build", false, 1196231808463658281], [7858942147296547339, "rustversion", false, 2785934287876839562], [10411997081178400487, "cfg_if", false, 12883859398113435013], [11382113702854245495, "wasm_bindgen_macro", false, 16815061810268796378]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/wasm-bindgen-d61d1c8a9fa11707/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}