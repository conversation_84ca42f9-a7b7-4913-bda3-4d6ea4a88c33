{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"either\"]", "target": 6209224040855486982, "profile": 2040997289075261528, "path": 13617227323256990275, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/writeable-2f47df5b5073f230/dep-lib-writeable", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}