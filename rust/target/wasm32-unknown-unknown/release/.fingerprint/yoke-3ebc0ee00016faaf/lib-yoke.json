{"rustc": 15497389221046826682, "features": "[\"alloc\", \"derive\", \"zerofrom\"]", "declared_features": "[\"alloc\", \"default\", \"derive\", \"serde\", \"zerofrom\"]", "target": 11250006364125496299, "profile": 2040997289075261528, "path": 8102406191783572710, "deps": [[2300794896071521484, "yoke_derive", false, 14336596512290577868], [4462517779602467004, "stable_deref_trait", false, 15200479311674305020], [17046516144589451410, "zerofrom", false, 6238120570088118439]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/yoke-3ebc0ee00016faaf/dep-lib-yoke", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}