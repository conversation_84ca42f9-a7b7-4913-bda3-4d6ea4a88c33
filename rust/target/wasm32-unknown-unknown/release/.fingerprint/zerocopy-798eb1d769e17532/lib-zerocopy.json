{"rustc": 15497389221046826682, "features": "[\"simd\"]", "declared_features": "[\"__internal_use_only_features_that_work_on_stable\", \"alloc\", \"derive\", \"float-nightly\", \"simd\", \"simd-nightly\", \"std\", \"zerocopy-derive\"]", "target": 3084901215544504908, "profile": 2040997289075261528, "path": 7458483187926014464, "deps": [[2377604147989930065, "build_script_build", false, 13558684059846850155]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/zerocopy-798eb1d769e17532/dep-lib-zerocopy", "checksum": false}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}