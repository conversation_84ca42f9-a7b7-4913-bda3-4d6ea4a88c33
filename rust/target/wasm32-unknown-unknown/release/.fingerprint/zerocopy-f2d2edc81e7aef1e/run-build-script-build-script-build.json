{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2377604147989930065, "build_script_build", false, 16516674905904334071]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/release/build/zerocopy-f2d2edc81e7aef1e/output", "paths": ["build.rs", "Cargo.toml"]}}], "rustflags": ["--cfg=wasm_bindgen", "--cfg", "getrandom_backend=\"wasm_js\""], "config": 0, "compile_kind": 0}