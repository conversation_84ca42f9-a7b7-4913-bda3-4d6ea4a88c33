{"rustc": 15497389221046826682, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2040997289075261528, "path": 14696731576387328703, "deps": [[9620753569207166497, "zerovec_derive", false, 13486514774564819348], [10706449961930108323, "yoke", false, 6230495877441441578], [17046516144589451410, "zerofrom", false, 6238120570088118439]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/zerovec-3efa2479b078421f/dep-lib-zerovec", "checksum": false}}], "rustflags": ["--cfg", "getrandom_backend=\"wasm_js\""], "config": 2069994364910194474, "compile_kind": 14682669768258224367}